# Samsung cancelDraw 메시지를 필터링하는 Flutter 실행 스크립트
# 참조: https://github.com/flutter/flutter/issues/111593

param(
    [string]$Mode = "debug",
    [string]$Device = ""
)

Write-Host "🚀 Samsung cancelDraw 최적화된 Flutter 실행" -ForegroundColor Green
Write-Host "💡 cancelDraw 메시지를 필터링하여 깔끔한 로그를 제공합니다" -ForegroundColor Yellow

$flutterCmd = "flutter run --$Mode"
if ($Device -ne "") {
    $flutterCmd += " --device-id $Device"
}

Write-Host "📱 실행 명령어: $flutterCmd" -ForegroundColor Cyan

# cancelDraw 관련 메시지 필터링
Invoke-Expression $flutterCmd | Where-Object {
    $_ -notmatch "\[DP\] cancelDraw" -and
    $_ -notmatch "ViewRootImpl.*cancelDraw" -and
    $_ -notmatch "FlutterActivityAndFragmentDelegate.*isViewVisible"
} | ForEach-Object {
    # 중요한 로그는 색상으로 강조
    if ($_ -match "🎯 CancelDrawMonitor") {
        Write-Host $_ -ForegroundColor Green
    } elseif ($_ -match "ERROR|FATAL") {
        Write-Host $_ -ForegroundColor Red
    } elseif ($_ -match "WARNING|WARN") {
        Write-Host $_ -ForegroundColor Yellow
    } elseif ($_ -match "Samsung.*최적화") {
        Write-Host $_ -ForegroundColor Magenta
    } else {
        Write-Host $_
    }
}

Write-Host "`n✅ Samsung cancelDraw 필터링 완료" -ForegroundColor Green
