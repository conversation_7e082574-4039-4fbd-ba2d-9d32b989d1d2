import 'dart:async';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import '../utils/image_cache.dart';
import '../utils/logger_utils.dart';
import '../utils/mobile_performance_utils.dart';
import 'package:intl/date_symbol_data_local.dart';

/// 공통 초기화 (플러터 바인딩, 시스템 UI, Firebase 등)
Future<void> bootstrapApplication() async {
  WidgetsFlutterBinding.ensureInitialized();

  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarBrightness: Brightness.light,
    statusBarIconBrightness: Brightness.dark,
    systemNavigationBarColor: Colors.white,
    systemNavigationBarDividerColor: Colors.transparent,
    systemNavigationBarIconBrightness: Brightness.dark,
  ));

  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge, overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom]);

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  ImageCacheManager.initialize();
  ImageCacheManager.schedulePeriodicOptimization();

  try {
    await Firebase.initializeApp();
    LoggerUtils.logInfo('Firebase 초기화 완료 (기본 앱)', tag: 'Bootstrap');

    // Firebase App Check 초기화 (임시 비활성화 - iOS JSON 오류 해결)
    try {
      LoggerUtils.logInfo('Firebase App Check 임시 비활성화', tag: 'Bootstrap');
      // App Check 관련 초기화 완전 비활성화
    } catch (e) {
      LoggerUtils.logError('Firebase App Check 초기화 건너뜀', tag: 'Bootstrap', error: e);
    }

    // Firebase Analytics 로그 최적화 (개발 중에는 비활성화)
    try {
      await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(false);
      LoggerUtils.logInfo('Firebase Analytics 비활성화 완료', tag: 'Bootstrap');
    } catch (e) {
      LoggerUtils.logError('Firebase Analytics 설정 실패', tag: 'Bootstrap', error: e);
    }
  } catch (e) {
    LoggerUtils.logError('Firebase 초기화 실패', tag: 'Bootstrap', error: e);
    // Firebase 초기화 실패해도 앱은 계속 실행
  }

  await initializeDateFormatting('ko_KR', null);
  // 성능 최적화 초기화 (레거시 main.dart 흐름 복원)
  MobilePerformanceUtils.initializeOptimizations();
}
