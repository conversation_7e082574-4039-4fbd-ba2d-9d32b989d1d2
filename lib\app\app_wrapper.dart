import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/logger_utils.dart';
import '../providers/nickname_provider.dart';
import '../providers/unified_workspace_provider.dart';
import '../providers/settings_provider.dart';
import '../providers/product_provider.dart';
import '../providers/prepayment_provider.dart';
import '../providers/seller_provider.dart';
import '../providers/data_sync_provider.dart';
import '../services/differential_sync_service.dart';
import '../services/realtime_sync_service_main.dart';
import '../services/database_service.dart';
import '../utils/device_utils.dart';
import '../utils/app_colors.dart';
import '../screens/onboarding/event_workspace_onboarding_screen.dart';
import '../screens/records_and_statistics/records_and_statistics_screen.dart';
import '../screens/home/<USER>';
import '../screens/sale/sale_screen.dart';
import '../screens/settings/my_page_screen.dart';
import '../screens/auth/nickname_screen.dart';
import '../widgets/confirmation_dialog.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../utils/mobile_performance_utils.dart';
import '../utils/state_sync_manager.dart';
import '../utils/network_status.dart';
import '../utils/common_utils.dart';
import '../utils/toast_utils.dart';

/// 앱 래퍼 위젯
class AppWrapper extends ConsumerStatefulWidget {
  final Widget child;
  const AppWrapper({super.key, required this.child});
  @override
  ConsumerState<AppWrapper> createState() => _AppWrapperState();
}

class _AppWrapperState extends ConsumerState<AppWrapper> with WidgetsBindingObserver {
  static const String _tag = 'AppWrapper';
  int _currentTabIndex = 0; // 하단 탭 인덱스

  @override
  void initState() {
    super.initState();
    LoggerUtils.methodStart('initState', tag: _tag);
    WidgetsBinding.instance.addObserver(this);
    // 백그라운드에서 초기화 수행 (UI 블로킹 없음)
    _initializeAppDataInBackground();
    LoggerUtils.methodEnd('initState', tag: _tag);
  }

  void _initializeAppDataInBackground() async {
    try {
      LoggerUtils.logInfo('백그라운드 앱 초기화 시작', tag: _tag);

      // 1단계: 기기 타입 감지 및 설정
      LoggerUtils.logInfo('[Init] 기기 타입 감지 시작', tag: _tag);
      await _detectAndSetDeviceType();
      
      // 2단계: 필수 데이터 로드 (스플래시에서 이미 로드된 것 제외)
      LoggerUtils.logInfo('[Init] 설정 로드 시작', tag: _tag);
      await ref.read(settingsNotifierProvider.notifier).loadSettings();
      // 닉네임은 스플래시에서 이미 로드됨 - 중복 로딩 제거

      // 실시간 동기화 서비스 초기화
      try {
        LoggerUtils.logInfo('[Init] 실시간 동기화 서비스 초기화 확인', tag: _tag);
        final realtimeSyncService = RealtimeSyncService();
        if (!realtimeSyncService.isInitialized.value) {
          await realtimeSyncService.initialize();
        }
        LoggerUtils.logInfo('실시간 동기화 서비스 초기화 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('실시간 동기화 서비스 초기화 실패', tag: _tag, error: e);
      }

      // 백그라운드에서 나머지 데이터 로드
      _loadDataInBackground();

      LoggerUtils.logInfo('백그라운드 앱 초기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('백그라운드 앱 초기화 실패', tag: _tag, error: e);
    }
  }

  /// 백그라운드에서 나머지 데이터를 로드하는 메서드
  void _loadDataInBackground() {
    // UI가 표시된 후 백그라운드에서 실행
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        LoggerUtils.logInfo('백그라운드 데이터 로딩 시작', tag: _tag);

        // Firebase 사용자 로그인 상태 확인 및 스마트 동기화 수행
        final user = FirebaseAuth.instance.currentUser;
        if (user != null) {
          await _performDataSync();
        }

        // 행사 워크스페이스별 데이터 로드
        final workspaceState = ref.read(unifiedWorkspaceProvider);
        if (workspaceState.hasCurrentWorkspace) {
          await _loadWorkspaceData();
        }

        LoggerUtils.logInfo('백그라운드 데이터 로딩 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('백그라운드 데이터 로딩 실패', tag: _tag, error: e);
      }
    });
  }

  /// Firebase 데이터 동기화 수행 (SyncConfirmationScreen을 거치지 않은 경우에만)
  Future<void> _performDataSync() async {
    try {
      // SyncConfirmationScreen을 거쳤다면 이미 동기화가 완료되었으므로 건너뛰기
      final prefs = await SharedPreferences.getInstance();
      final syncCompleted = prefs.getBool('sync_completed_recently') ?? false;

      if (syncCompleted) {
        LoggerUtils.logInfo('최근 동기화 완료됨 - AppWrapper 동기화 건너뛰기', tag: 'AppWrapper');
        // 플래그 제거 (일회성)
        await prefs.remove('sync_completed_recently');
        return;
      }

      // 새 디바이스 감지 (로컬 데이터가 거의 없는 경우)
      final isNewDevice = await _detectNewDevice();

      if (isNewDevice) {
        LoggerUtils.logInfo('새 디바이스 감지 - 전체 다운로드 우선 실행', tag: 'AppWrapper');
        await _performFullDownloadForNewDevice();
        return;
      }

      LoggerUtils.logInfo('앱 시작 시 차분 동기화 시작', tag: 'AppWrapper');

      // 차분 동기화 서비스 생성
      final databaseService = ref.read(databaseServiceProvider);
      final diffSyncService = DifferentialSyncService(databaseService);

      // 1단계: 행사 목록 동기화 (메타데이터만)
      LoggerUtils.logInfo('1단계: 행사 목록 동기화 시작', tag: 'AppWrapper');
      final eventsResult = await diffSyncService.syncEventsList(
        onProgress: (message) {
          LoggerUtils.logInfo('행사 목록 동기화: $message', tag: 'AppWrapper');
        },
        onError: (error) {
          LoggerUtils.logError('행사 목록 동기화 오류: $error', tag: 'AppWrapper');
        }
      );

      LoggerUtils.logInfo(
        '행사 목록 동기화 완료 - 다운로드: ${eventsResult.downloaded}, 삭제: ${eventsResult.deleted}',
        tag: 'AppWrapper'
      );

      // 2단계: 현재 워크스페이스 확인 (행사 목록 변경으로 인해 삭제되었을 수 있음)
      final workspaceState = ref.read(unifiedWorkspaceProvider);
      if (!workspaceState.hasCurrentWorkspace) {
        LoggerUtils.logInfo('현재 워크스페이스 없음 - 현재 행사 데이터 동기화 건너뛰기', tag: 'AppWrapper');
        return;
      }

      final currentEventId = workspaceState.currentWorkspace!.id;
      LoggerUtils.logInfo('2단계: 현재 행사 데이터 동기화 시작 (ID: $currentEventId)', tag: 'AppWrapper');
      
      // 3단계: 현재 행사의 데이터만 차분 동기화
      final result = await diffSyncService.syncCurrentEventData(
        currentEventId,
        onProgress: (message) {
          LoggerUtils.logInfo('현재 행사 동기화: $message', tag: 'AppWrapper');
        },
        onError: (error) {
          LoggerUtils.logError('현재 행사 동기화 오류: $error', tag: 'AppWrapper');
        }
      );

      LoggerUtils.logInfo(
        '현재 행사 동기화 완료 - 다운로드: ${result.downloaded}, 업로드: ${result.uploaded}, 삭제: ${result.deleted}, 스킵: ${result.skipped}',
        tag: 'AppWrapper'
      );

      if (result.hasErrors) {
        LoggerUtils.logWarning('동기화 중 오류 발생: ${result.errors.join(', ')}', tag: 'AppWrapper');
      }

      LoggerUtils.logInfo('차분 동기화 완료 - 실시간 동기화가 자동으로 활성화됨', tag: 'AppWrapper');

    } catch (e) {
      LoggerUtils.logError('앱 시작 시 데이터 동기화 실패', tag: 'AppWrapper', error: e);
      // 동기화 실패해도 앱은 계속 실행
    }
  }

  /// 워크스페이스 데이터 로드
  Future<void> _loadWorkspaceData() async {
    try {
      final productNotifier = ref.read(productNotifierProvider.notifier);
      final prepaymentNotifier = ref.read(prepaymentNotifierProvider.notifier);

      // 에러 상태 클리어
      productNotifier.clearError();
      prepaymentNotifier.clearError();

      // 데이터베이스 락 방지를 위해 순차적으로 로드
      await productNotifier.loadProducts();
      await prepaymentNotifier.loadPrepayments(showLoading: false);
      await ref.read(sellerNotifierProvider.notifier).loadSellers();
    } catch (e) {
      LoggerUtils.logError('워크스페이스 데이터 로드 실패', tag: _tag, error: e);
    }
  }

  /// 기기 타입 감지 및 Provider에 설정
  Future<void> _detectAndSetDeviceType() async {
    try {
      if (!mounted) return;

      // DeviceUtils를 사용한 기기 타입 감지 (가장 신뢰할 만함)
      final isTablet = DeviceUtils.isTablet(context);

      // 기기 정보 로깅
      final mediaQuery = MediaQuery.of(context);
      final size = mediaQuery.size;

      LoggerUtils.logInfo('=== 기기 타입 감지 결과 ===', tag: _tag);
      LoggerUtils.logInfo('화면 크기: ${size.width.toStringAsFixed(1)} x ${size.height.toStringAsFixed(1)}dp', tag: _tag);
      LoggerUtils.logInfo('최소 변: ${size.shortestSide.toStringAsFixed(1)}dp', tag: _tag);
      LoggerUtils.logInfo('기기 타입: ${isTablet ? "태블릿" : "스마트폰"}', tag: _tag);

      // Provider에 기기 타입 설정
      await ref.read(settingsNotifierProvider.notifier).setDeviceType(isTablet);

    } catch (e, stackTrace) {
      LoggerUtils.logError('기기 타입 감지 실패', tag: _tag, error: e, stackTrace: stackTrace);
      // 감지 실패 시 스마트폰으로 기본 설정
      await ref.read(settingsNotifierProvider.notifier).setDeviceType(false);
    }
  }

  @override
  void dispose() {
    LoggerUtils.methodStart('dispose', tag: _tag);
    MobilePerformanceUtils.stopMemoryMonitoring();
    MobilePerformanceUtils.performMemoryCleanup();
    _cleanupOptimizationSystems();
    WidgetsBinding.instance.removeObserver(this);
    LoggerUtils.methodEnd('dispose', tag: _tag);
    super.dispose();
  }

  void _cleanupOptimizationSystems() {
    try {
      StateSyncManager().clearAllStates();
      LoggerUtils.logInfo('최적화 시스템 정리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('최적화 시스템 정리 실패', tag: _tag, error: e);
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    LoggerUtils.methodStart('didChangeAppLifecycleState', tag: _tag, data: {'state': state.name});
    if (state == AppLifecycleState.resumed) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _refreshProvidersIfNeeded();
        }
      });
    } else if (state == AppLifecycleState.detached || state == AppLifecycleState.paused) {
      // 앱 종료 시 메모리 누수 방지를 위한 정리
      try {
        NetworkStatusUtil.dispose();
        CommonUtils.disposeDebounceTimer();
        LoggerUtils.logInfo('앱 종료 시 메모리 정리 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('앱 종료 시 메모리 정리 실패', tag: _tag, error: e);
      }
    }
    LoggerUtils.methodEnd('didChangeAppLifecycleState', tag: _tag);
  }

  Future<void> _refreshProvidersIfNeeded() async {
    if (!mounted) return;
    LoggerUtils.methodStart('_refreshProvidersIfNeeded', tag: _tag);
    try {
      // 실시간 동기화가 활성화되어 있으므로 수동 새로고침은 불필요
      LoggerUtils.logInfo('실시간 동기화 활성화로 인해 수동 새로고침 생략', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('Provider 상태 새로고침 실패', tag: _tag, error: e, stackTrace: stackTrace);
    }
    LoggerUtils.methodEnd('_refreshProvidersIfNeeded', tag: _tag);
  }

  Future<bool> _detectNewDevice() async {
    try {
      final workspaceState = ref.read(unifiedWorkspaceProvider);
      final hasWorkspaces = workspaceState.workspaces.isNotEmpty;
      if (!hasWorkspaces) return true;
      final dataSyncService = ref.read(dataSyncServiceProvider);
      final hasServerData = await dataSyncService.hasServerData();
      if (hasServerData && workspaceState.workspaces.length <= 1) return true;
      return false;
    } catch (e) {
      return false;
    }
  }

  /// 새 디바이스를 위한 전체 다운로드 수행
  Future<void> _performFullDownloadForNewDevice() async {
    try {
      final dataSyncService = ref.read(dataSyncServiceProvider);

      LoggerUtils.logInfo('새 디바이스 전체 다운로드 시작', tag: _tag);

      await dataSyncService.performBidirectionalSync(
        onProgress: (message) {
          LoggerUtils.logInfo('새 디바이스 동기화: $message', tag: _tag);
        },
        onError: (error) {
          LoggerUtils.logError('새 디바이스 동기화 오류: $error', tag: _tag);
        },
      );

      LoggerUtils.logInfo('새 디바이스 전체 다운로드 완료', tag: _tag);

      // 워크스페이스 Provider 새로고침
      await ref.read(unifiedWorkspaceProvider.notifier).refresh();

    } catch (e) {
      LoggerUtils.logError('새 디바이스 전체 다운로드 실패', tag: _tag, error: e);
      // 실패해도 차분 동기화는 계속 진행
    }
  }

  void _onTabTapped(int index) {
    if (_currentTabIndex != index) {
      setState(() => _currentTabIndex = index);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
      child: PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) async {
          if (didPop) return;
          final shouldExit = await ConfirmationDialog.show(
            context: context,
            title: '앱 종료',
            message: '앱을 종료하시겠습니까?',
            confirmLabel: '예',
            cancelLabel: '아니오',
          );
          if (shouldExit == true && context.mounted) {
            if (Theme.of(context).platform == TargetPlatform.android ||
                Theme.of(context).platform == TargetPlatform.iOS) {
              SystemNavigator.pop();
            } else {
              // 다른 플랫폼 (Web, Desktop 등)
              Navigator.of(context).popUntil((route) => route.isFirst);
            }
          }
        },
        child: Consumer(
          builder: (context, ref, _) {
            final nickname = ref.watch(nicknameProvider);

            if (nickname == null) {
              LoggerUtils.logInfo('→ NicknameScreen으로 이동', tag: 'AppWrapper');
              return NicknameScreen(
                onNicknameSet: () {
                  setState(() {}); // 닉네임 등록 후 화면 갱신
                },
              );
            }

            return _buildWorkspaceCheck();
          },
        ),
      ),
    );
  }

  /// 워크스페이스 확인 로직
  Widget _buildWorkspaceCheck() {
    // 닉네임이 있으면 행사 워크스페이스 존재 확인
    final workspaceState = ref.watch(unifiedWorkspaceProvider);

    if (!workspaceState.hasWorkspaces) {
      // 행사 워크스페이스가 없으면 행사 워크스페이스 생성 온보딩 화면으로 이동
      return EventWorkspaceOnboardingScreen(
        onWorkspaceCreated: () {
          LoggerUtils.logInfo('행사 워크스페이스 생성 완료 - UI 갱신', tag: _tag);
          if (mounted) {
            setState(() {});
          }
        },
      );
    }

    // 행사 워크스페이스는 있지만 현재 행사 워크스페이스가 설정되지 않은 경우
    if (!workspaceState.hasCurrentWorkspace) {
      // 로딩 화면 표시
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('행사 워크스페이스 정보를 불러오는 중...'),
            ],
          ),
        ),
      );
    }

    // 정상적으로 현재 행사 워크스페이스가 설정된 경우 하단 탭 네비게이션 표시
    return _buildMainScreenWithBottomTabs();
  }

  Widget _buildMainScreenWithBottomTabs() {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: _buildCurrentTabContent(),
      bottomNavigationBar: _buildBottomNavigationBar(),
      floatingActionButton: _buildFloatingPOSButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  Widget _buildCurrentTabContent() {
    return IndexedStack(
      index: _currentTabIndex,
      children: [
        _buildHomeTab(),
        widget.child,
        Container(),
        const RecordsAndStatisticsScreen(),
        _buildMyTab(),
      ],
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Color(0xFFE0E0E0), // 11번가 스타일 구분선
            width: 1.0,
          ),
        ),
      ),
      child: BottomAppBar(
        shape: const CircularNotchedRectangle(), // 중앙에 노치 생성
        notchMargin: 8.0, // 노치 여백
        color: Colors.white, // 깔끔한 하얀색 배경
        elevation: 0.0, // Container에서 구분선 처리하므로 elevation 제거
        child: SizedBox(
          height: 60,
          child: Row(
            children: [
              Expanded(child: _buildNavItem(0, LucideIcons.home, '홈')),
              Expanded(child: _buildNavItem(1, LucideIcons.creditCard, '선입금')),
              Expanded(
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInOut,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: _currentTabIndex == 2 ? RadialGradient(
                      center: Alignment.center,
                      radius: 1.2,
                      colors: [
                        AppColors.primarySeed.withValues(alpha: 0.15), // 중앙 진함
                        AppColors.primarySeed.withValues(alpha: 0.08), // 중간
                        AppColors.primarySeed.withValues(alpha: 0.03), // 가장자리 연함
                        Colors.transparent, // 완전 투명
                      ],
                      stops: const [0.0, 0.4, 0.7, 1.0],
                    ) : null,
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        // POS 버튼과 동일하게 SaleScreen으로 이동
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const SaleScreen(),
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(12),
                      splashColor: AppColors.primarySeed.withValues(alpha: 0.25), // 더 진한 ripple
                      highlightColor: AppColors.primarySeed.withValues(alpha: 0.12), // 더 진한 highlight
                      child: SizedBox(
                        width: double.infinity,
                        height: double.infinity,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            const SizedBox(height: 32), // FloatingActionButton 공간 확보
                            Text(
                              'POS',
                              style: TextStyle(
                                color: const Color(0xFF9E9E9E), // 항상 비활성 색상으로 표시
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 4), // 하단 여백
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ), // 중앙 FAB 공간 + POS 텍스트
              Expanded(child: _buildNavItem(3, LucideIcons.fileBarChart, '기록&통계')),
              Expanded(child: _buildNavItem(4, LucideIcons.user, 'MY')),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(int index, IconData icon, String label) {
    final isSelected = _currentTabIndex == index;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200), // 부드러운 애니메이션 전환
      curve: Curves.easeInOut,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        // 선택된 아이템에 그라데이션 효과 적용
        gradient: isSelected ? RadialGradient(
          center: Alignment.center,
          radius: 1.2, // 더 자연스러운 확산
          colors: [
            AppColors.primarySeed.withValues(alpha: 0.15), // 중앙 진함
            AppColors.primarySeed.withValues(alpha: 0.08), // 중간
            AppColors.primarySeed.withValues(alpha: 0.03), // 가장자리 연함
            Colors.transparent, // 완전 투명
          ],
          stops: const [0.0, 0.4, 0.7, 1.0], // 그라데이션 구간 세밀하게 조정
        ) : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _onTabTapped(index),
          borderRadius: BorderRadius.circular(12),
          // 더 미묘한 ripple 색상
          splashColor: AppColors.primarySeed.withValues(alpha: 0.15),
          highlightColor: AppColors.primarySeed.withValues(alpha: 0.08),
          child: Container(
            width: double.infinity,
            height: double.infinity,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: 24,
                  color: isSelected 
                    ? AppColors.primarySeed // 선택된 아이템은 primarySeed 색상
                    : const Color(0xFF9E9E9E), // 비선택 아이템은 회색
                ),
                const SizedBox(height: 4),
                Text(
                  label,
                  style: TextStyle(
                    color: isSelected 
                      ? AppColors.primarySeed // 선택된 아이템은 primarySeed 색상
                      : const Color(0xFF9E9E9E), // 비선택 아이템은 회색
                    fontSize: 12,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingPOSButton() {
    return Transform.translate(
      offset: const Offset(0, 8), // 버튼을 아래로 8px 이동
      child: FloatingActionButton(
        onPressed: () {
          // 새로운 페이지로 이동 (하단 앱바 숨김)
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const SaleScreen(),
            ),
          );
        },
        backgroundColor: AppColors.primarySeed, // 앱바와 같은 웜 테라코타
        foregroundColor: Colors.white, // 흰색 아이콘
        elevation: 8.0,
        child: const Icon(
          LucideIcons.shoppingCart,
          size: 28,
        ),
      ),
    );
  }

  Widget _buildHomeTab() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('리더보드'),
        centerTitle: true,
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        surfaceTintColor: Colors.transparent, // Material 3에서 색상 덮어쓰기 방지
        elevation: 0,
        automaticallyImplyLeading: false, // 뒤로가기 버튼 제거
        actions: [
          // 테스트용 토스트 버튼
          IconButton(
            onPressed: () {
              ToastUtils.showMessage(
                context,
                '이것은 2줄 이상의 긴 토스트 메시지입니다. POS 버튼 위에 나타나고 위로 확장되는지 테스트해보세요.',
                actionLabel: '확인',
                onAction: () {
                  ToastUtils.showSuccess(context, '액션이 실행되었습니다!');
                },
              );
            },
            icon: const Icon(Icons.message),
            tooltip: '토스트 테스트',
          ),
        ],
      ),
      body: const HomeDashboardScreen(),
    );
  }

  Widget _buildMyTab() => const MyPageScreen();
}

/// 앱 종료 시 메모리 누수 방지를 위한 통합 정리 함수
///
/// 중위험 메모리 누수 항목들의 정적 리소스를 모두 정리합니다.
/// 앱의 생명주기 종료 시점에 호출되어야 합니다.
Future<void> cleanupAppResources() async {
  try {
    // Utils 클래스들의 정적 리소스 정리
    MobilePerformanceUtils.shutdown();
    LoggerUtils.shutdown();

    // Services 클래스들의 정적 리소스 정리
    RealtimeSyncService.shutdown();

    LoggerUtils.logInfo('앱 리소스 정리 완료');
  } catch (e) {
    // 정리 중 오류가 발생해도 앱 종료는 계속 진행
    LoggerUtils.logError('앱 리소스 정리 중 오류 발생', error: e);
  }
}
